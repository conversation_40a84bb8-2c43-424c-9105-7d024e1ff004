import axios from 'axios-1.4';
import { withSessionApiRoute } from 'lib/with-session';
import { NextApiRequest, NextApiResponse } from 'next';
import EnrollmentAPI from 'src/services/EnrollmentAPI';
import { CreateCustomerNextBody } from 'src/services/EnrollmentAPI/types';
import * as CONSTANTS from 'src/utils/constants';
import { ErrorReturn } from 'src/utils/util';

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
async function handler(req: NextApiRequest, res: NextApiResponse) {
  const access_token = req.session.user?.access_token;
  if (access_token) {
    switch (req.method) {
      case 'POST': {
        const body = req.body as CreateCustomerNextBody;
        try {
          console.log('Create Customer' + req.body);

          let billingCity = '';
          let billingState = '';
          let billingStreetName = '';
          let billingStreetNumber = '';
          let billingUnitNumber = '';
          let billingPostalCode = '';
          if (body.billingOption === 'sameAddress') {
            billingCity = body.city;
            billingState = body.state;
            billingStreetName = body.streetName;
            billingStreetNumber = body.streetNumber;
            billingUnitNumber = body.unitNumber;
            billingPostalCode = body.postalCode;
          } else if (body.billingOption === 'differentAddress') {
            billingCity = body.billingCity;
            billingState = body.billingState;
            billingStreetName = body.billingStreetAddress;
            billingStreetNumber = body.billingStreetNumber;
            billingUnitNumber = body.billingAptOrUnit;
            billingPostalCode = body.billingZipCode;
          } else if (body.billingOption === 'poBox') {
            billingCity = body.poBoxCity;
            billingState = body.poBoxState;
            billingPostalCode = body.poBoxZipCode;
          }
          // const SelectedESIIDStatusBeforeCall =
          //   await EnrollmentAPI.getPendingTransactionAndActiveESIIDStatus(body.esiid, access_token);
          // //MovingInOwnPremise - check isActive true
          // if (SelectedESIIDStatusBeforeCall.data?.result?.isActive) {
          //   res.status(200).json({
          //     SelectedESIIDStatusBeforeResponse: SelectedESIIDStatusBeforeCall.data,
          //   });
          //   return;
          // }

          // //EsiFutureMoveInContract - check isPending true
          // if (SelectedESIIDStatusBeforeCall.data?.result?.isPending) {
          //   res.status(200).json({
          //     SelectedESIIDStatusBeforeResponse: SelectedESIIDStatusBeforeCall.data,
          //   });
          //   return;
          // }

          // const enrollmentAPI = new EnrollmentAPI(access_token);
          const createCustomerReq = await EnrollmentAPI.createCustomer(
            {
              email: body.email,
              firstName: body.firstName,
              lastName: body.lastName,
              phoneNumber: body.phoneNumber,
              isMobile: body.isMobile,
              dateOfBirth: body.dateOfBirth,
              esiid: body.esiid,
              socialSecurityNumber: body.socialSecurityNumber,
              driverLicenseNumber: body.driverLicenseNumber,
              driverLicenseState: body.driverLicenseState,
              middleName: body.middleName,
              correspondenceLanguage: body.correspondanceLanguage,
              channel: body.channel,
              vendorId: body.vendorId,
              poBox: body.poBox,
              poBoxCity: billingCity,
              poBoxState: billingState,
              poBoxZipCode: billingPostalCode,
              skiptdvalidation: body.skiptdvalidation,
              city: billingCity,
              streetName: billingStreetName,
              streetNumber: billingStreetNumber,
              unit: billingUnitNumber,
              postalCode: billingPostalCode,
              state: billingState,
              DRSActionToken: body.DRSActionToken,
              sessionId: body.sessionId,
              CNumber: body.CNumber,
              ANumber: body.ANumber,
              MFAReferralId: body.MFAReferralId,
            },
            access_token
          );

          console.log(`Create Customer Req: ${JSON.stringify(createCustomerReq.data, null, 4)}`);
          if (createCustomerReq.data.result.partnerNumber === null) {
            res.status(500).send('BP Number is Null');
          }
          const { partnerNumber: bpNumber, priorDebtStatus } = createCustomerReq.data.result;

          const checkSecurityDepositReq = await EnrollmentAPI.checkSecurityDeposit(
            {
              bpNumber: bpNumber,
              isMultiFamilyDwelling: body.isMultiFamilyDwelling,
              priorDebtStatus: priorDebtStatus,
              streetName: body.streetName,
              streetNumber: body.streetNumber,
              city: body.city,
              state: body.state,
              postalCode: body.postalCode,
              esiid: body.esiid,
              unitNumber: body.unitNumber,
              firstName: body.firstName,
              lastName: body.lastName,
              socialSecurityNumber: body.socialSecurityNumber,
              mobileNumber: body.phoneNumber,
              dateOfBirth: body.dateOfBirth,
              driverLicenseNumber: body.driverLicenseNumber,
              driverLicenseState: body.driverLicenseState,
              channel: body.channel,
              vendorId: body.vendorId,
              Email: body.email,
            },
            access_token
          );

          console.log(
            `Check security deposit check: ${JSON.stringify(checkSecurityDepositReq.data, null, 4)}`
          );

          if (checkSecurityDepositReq.data.result.canCreateContract === false) {
            res.status(500).send('canCreateContract is false');
          }

          let calculateDepositReq = null;
          if (
            checkSecurityDepositReq.data &&
            checkSecurityDepositReq.data.result &&
            checkSecurityDepositReq.data.result.isDepositRequired
          )
            calculateDepositReq = await EnrollmentAPI.calculateDeposit(
              bpNumber,
              body.esiid,
              body.productId,
              body.dwellingType,
              access_token
            );

          console.log(`Calculate deposit: ${JSON.stringify(calculateDepositReq?.data, null, 4)}`);

          const createContractAccountReq = await EnrollmentAPI.createContractAccount(
            {
              bpNumber,
              city: billingCity,
              streetName: billingStreetName,
              streetNumber: billingStreetNumber,
              unit: billingUnitNumber,
              postalCode: billingPostalCode,
              state: billingState,
              country: 'US',
              language: body.correspondanceLanguage,
              channel: body.channel,
              vendorId: body.vendorId,
              PoBox: body.poBox,
              COName: '', //it is an optional field because we don't have the field in the UI
            },
            access_token
          );

          console.log(
            `Create contract account: ${JSON.stringify(createContractAccountReq.data, null, 4)}`
          );

          const { contractAccount: contractAccountNumber } = createContractAccountReq.data.result;

          if (
            createContractAccountReq.data.result.indicator === CONSTANTS.API_SUCCESS_MSG &&
            createContractAccountReq.data.result.contractAccount
          ) {
            // const SelectedESIIDStatus =
            //   await EnrollmentAPI.getPendingTransactionAndActiveESIIDStatus(
            //     body.esiid,
            //     access_token
            //   );

            // //Need to check partnerNumber after updated in api with prefix '00' in API
            // if (
            //   SelectedESIIDStatus.data?.result?.isActive &&
            //   SelectedESIIDStatus.data?.result?.partnerNumber !== bpNumber
            // ) {
            //   console.log(
            //     `Selecetd Esiid Status: ${JSON.stringify(SelectedESIIDStatus.data, null, 4)}`
            //   );
            //   res.status(200).json({
            //     createCustomerResponse: createCustomerReq.data,
            //     checkSecurityDepositResponse: checkSecurityDepositReq.data,
            //     calculateDepositResponse: calculateDepositReq?.data,
            //     createContractAccountResponse: createContractAccountReq.data,
            //     SelectedESIIDStatusResponse: SelectedESIIDStatus.data,
            //     connectValidateResponse: null,
            //   });
            // } else {
            const connectValidate = await EnrollmentAPI.connectValidate(
              {
                bpNumber,
                contractAccountNumber,
                esiid: body.esiid,
                startDate: body.startDate,
                customerIntent: body.customerIntent,
                dwellingType: body.dwellingType,
                productId: body.productId,
                incentiveId: body.incentiveId,
                promoCode: body.promoCode,
                campaignId: body.campaignId,
                channel: body.channel,
                vendorId: body.vendorId,
                enrollDate: body.enrollDate,
                WebExperienceId: body.WebExperienceId,
                CNumber: body.CNumber,
                ANumber: body.ANumber,
                ReferralId: body.MFAReferralId ?? '',
                autopayEligible: checkSecurityDepositReq?.data?.result?.autopayEligible,
                hasNoDeposit: !checkSecurityDepositReq?.data?.result?.isDepositRequired,
                cardDetails: null,
                bankDetails: null,
              },
              access_token
            );

            if (
              connectValidate.data.result.indicator === CONSTANTS.API_ENROLL_SUBMIT_MSG ||
              connectValidate.data.result.indicator === CONSTANTS.API_MovingInOwnPremise_MSG ||
              connectValidate.data.result.indicator === CONSTANTS.API_ActiveContractExists_MSG ||
              connectValidate.data.result.indicator === CONSTANTS.SwitchHoldUnknown
            ) {
              console.log(`Connect validate: ${JSON.stringify(connectValidate.data, null, 4)}`);
              res.status(200).json({
                createCustomerResponse: createCustomerReq.data,
                checkSecurityDepositResponse: checkSecurityDepositReq.data,
                calculateDepositResponse: calculateDepositReq?.data,
                createContractAccountResponse: createContractAccountReq.data,
                connectValidateResponse: connectValidate.data,
              });
            }

            if (connectValidate.data.result.indicator !== CONSTANTS.API_SUCCESS_MSG) {
              res.status(500).send('Unable to validate the connect call');
            }
            console.log(`Connect validate: ${JSON.stringify(connectValidate.data, null, 4)}`);
            res.status(200).json({
              createCustomerResponse: createCustomerReq.data,
              checkSecurityDepositResponse: checkSecurityDepositReq.data,
              calculateDepositResponse: calculateDepositReq?.data,
              createContractAccountResponse: createContractAccountReq.data,
              connectValidateResponse: connectValidate.data,
            });
            // }
          } else {
            res.status(500).send('Unable to create the contract');
          }
        } catch (err) {
          if (axios.isAxiosError(err)) {
            console.log(
              `Error occured when creating customer: ${JSON.stringify(err.toJSON(), null, 4)}`
            );
          } else {
            console.log(err);
          }
          const errorcheck = await ErrorReturn(err);
          res.status(500).send(errorcheck);
        }
      }
      default: {
        res.status(405).end();
      }
    }
  } else {
    res.status(401).end();
  }
}

export default withSessionApiRoute(handler);
