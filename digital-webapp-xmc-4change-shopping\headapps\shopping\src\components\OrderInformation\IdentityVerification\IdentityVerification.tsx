import { Select, TextInput } from '@mantine/core';
import { UseFormReturnType } from '@mantine/form';
import { Field, RichText, Text, withDatasourceCheck } from '@sitecore-jss/sitecore-jss-nextjs';
import Button from 'components/Elements/Button/Button';
import { ComponentProps } from 'lib/component-props';
import React, { useEffect } from 'react';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { OrderInfoFormType } from '../OrderInformationContainer/OrderInformationContainer';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronDown, faLock } from '@fortawesome/pro-light-svg-icons';
import { faCircleCheck } from '@fortawesome/pro-solid-svg-icons';

type IdentityVerificationProps = ComponentProps & {
  fields: {
    IdentityInformationTitleText: Field<string>;
    Description: Field<string>;
    SocialSecurityNumberOptionText: Field<string>;
    ORText: Field<string>;
    DrivingLicenceOptionText: Field<string>;
    SocialSecurityNumberText: Field<string>;
    SocialSecurityNumberToolTipText: Field<string>;
    DisclaimerText: Field<string>;
    NextButtonText: Field<string>;
    NextButtonLink: Field<string>;
    DriversLicenseStates: { displayName: string }[];
    DefaultDriversLicenseState?: Field<string>;
    HideDriverLicense: Field<Boolean>;
  };
  form: UseFormReturnType<OrderInfoFormType>;
  setIdentityOption: React.Dispatch<React.SetStateAction<string>>;
  isFocused: boolean;
};

const IdentityVerification = (props: IdentityVerificationProps): JSX.Element => {
  useEffect(() => {
    if (
      props.fields?.DefaultDriversLicenseState?.value &&
      props.fields?.DriversLicenseStates?.length > 0
    ) {
      const stateDefaultValue = props.fields?.DriversLicenseStates?.filter(
        (plan) => plan.displayName === props.fields?.DefaultDriversLicenseState?.value
      );
      if (!props.form?.values?.driverLicenseState) {
        props.form.setFieldValue('driverLicenseState', stateDefaultValue[0]?.displayName);
      }
    }
  }, [props.form, props.fields?.DriversLicenseStates, props.fields?.DefaultDriversLicenseState]);

  const isSSN = props.form.values.identityOption === 'ssn';
  function isValidDrivingLicense(value: string): boolean {
    const phoneRegex = /^\d{10}$/;
    return phoneRegex.test(value);
  }

  const formatSSN = (event: React.ChangeEvent<HTMLInputElement>) => {
    const inputSSN = event.target.value.replace(/\D/g, ''); // Remove non-numeric characters
    let formatted = '';
    // Format the SSN as "XXX-XX-XXXX" while typing
    if (inputSSN.length > 0) {
      formatted = inputSSN.slice(0, 3);
      if (inputSSN.length > 3) {
        formatted += '-' + inputSSN.slice(3, 5);
        if (inputSSN.length > 5) {
          formatted += '-' + inputSSN.slice(5, 9);
        }
      }
    }
    props.form.setFieldValue('socialSecurityNumber', formatted);
  };
  return (
    <div
      className="identity-verification sm:-mt-[4rem] mt-[-10px] mb-[20px] sm:mb-[-20px] flex flex-col sm:w-[800px] px-6 sm:px-0 gap-5 wide:w-full ipad:w-full wide:px-[20px] ipad:px-[40px] w-full"
      id="identityRef"
    >
      <div className="mt-[-10px]">
        <Text
          tag="p"
          className="relative top-20  ml-7 font-primaryBold text-[24px] text-textQuattuordenary  text-plus1 -tracking-[0.25px] sm:text-plus3 sm:-tracking-[0.46px] sm:ml-[32px]"
          field={props.fields.IdentityInformationTitleText}
        />
      </div>
      <div className="flex flex-col border-2 border-borderSeptendenary rounded-xl px-[30px] py-[30px] w-full gap-[24px] sm:max-w-[864px] relative">
        <FontAwesomeIcon
          icon={faLock}
          className="absolute block top-[-10px] text-textSecondary left-[25px] bg-white w-[30px]"
        />
        <RichText
          className="mt-10 text-base font-primaryRegular text-textQuattuordenary"
          field={props.fields.Description}
        />
        {props?.fields?.HideDriverLicense?.value === false && (
          <div className="flex flex-col items-center gap-1 sm:flex-row sm:gap-5">
            <Button
              type="button"
              className={`w-full sm:w-[295px] ${
                isSSN
                  ? 'text-textQuinary bg-buttonSecondary focus:outline-none focus:ring-0'
                  : 'text-textQuinary  hover:border-borderPrimary focus:outline-none focus:ring-0 hover:text-textQuinary'
              }`}
              variant={isSSN ? 'primary' : 'secondary'}
              onClick={() => {
                props.form.setFieldValue('identityOption', 'ssn');
              }}
            >
              {props.fields.SocialSecurityNumberOptionText.value}
            </Button>

            <>
              <Text
                tag="p"
                className="text-[18px] leading-[30px] text-textQuattuordenary "
                field={props.fields.ORText}
              />
              <Button
                type="button"
                className={`w-full sm:w-[295px] ${
                  !isSSN
                    ? 'text-textQuinary bg-buttonSecondary focus:outline-none focus:ring-0'
                    : '  text-textQuinary hover:border-borderPrimary focus:outline-none focus:ring-0 hover:text-textQuinary'
                }`}
                variant={!isSSN ? 'primary' : 'secondary'}
                onClick={() => {
                  props.form.setFieldValue('identityOption', 'dl');
                }}
              >
                {props.fields.DrivingLicenceOptionText.value}
              </Button>
            </>
          </div>
        )}
        {isSSN && (
          <TextInput
            className="p-0  "
            onPaste={(e) => {
              e.preventDefault();
              return false;
            }}
            sx={{
              width: '100%',
              [`@media (min-width: 767px)`]: {
                width: '280px',
              },
            }}
            maxLength={11}
            label={props.fields.SocialSecurityNumberText.value}
            rightSection={
              <FontAwesomeIcon
                icon={faCircleCheck}
                className={`text-textSexdenary mr-[4px] text-plus2 ${
                  props.form.values.socialSecurityNumber.length >= 11 ? 'visible' : 'invisible'
                }`}
              />
            }
            withAsterisk
            value={props.form.values.socialSecurityNumber}
            onChange={(event) => formatSSN(event)}
            onBlur={(event) => formatSSN(event)}
            error={props.form.errors.socialSecurityNumber}
            // {...props.form.getInputProps('socialSecurityNumber')}
            // value={props.form.values.socialSecurityNumber.replace(
            //   /(\d{3})(\d{2})(\d{4})/,
            //   '$1-$2-$3'
            // )}
          />
        )}
        {!isSSN && (
          <div className="flex flex-col sm:flex-row gap-6">
            <TextInput
              sx={{
                width: '280px',
              }}
              maxLength={12}
              label={props.fields.DrivingLicenceOptionText.value}
              className=""
              rightSection={
                <FontAwesomeIcon
                  icon={faCircleCheck}
                  className={`text-textSexdenary text-plus2 ${
                    isValidDrivingLicense(props.form.values.driverLicenseNumber)
                      ? 'visible'
                      : 'invisible'
                  } mr-[4px]`}
                />
              }
              withAsterisk
              {...props.form.getInputProps('driverLicenseNumber')}
            />
            <div className="w-fit">
              <Select
                styles={{
                  wrapper: {
                    width: '156px',
                  },
                }}
                className=""
                data={props.fields.DriversLicenseStates.map((state) => state.displayName)}
                label="State Issued"
                {...props.form.getInputProps('driverLicenseState')}
                rightSection={
                  <FontAwesomeIcon
                    icon={faChevronDown}
                    className="text-textSecondary hover:text-hoverPrimary"
                  />
                }
                value={props.form?.values?.driverLicenseState}
                selectOnBlur
                searchable
              />
            </div>
          </div>
        )}
        <RichText
          tag="p"
          className=" text-textQuattuordenary sm:text-minus2 text-minus3"
          field={props.fields.DisclaimerText}
        />
      </div>
    </div>
  );
};

export { IdentityVerification };
const Component = withDatasourceCheck()<IdentityVerificationProps>(IdentityVerification);
export default aiLogger(Component, Component.name);
