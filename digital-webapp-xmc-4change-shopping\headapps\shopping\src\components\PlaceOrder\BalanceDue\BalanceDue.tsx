import {
  Text,
  Field,
  withDatasourceCheck,
  useSitecoreContext,
  RichText,
} from '@sitecore-jss/sitecore-jss-nextjs';
import { ComponentProps } from 'lib/component-props';
import dayjs from 'dayjs';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { useAppSelector } from 'src/stores/store';
import PriorDebtTable from '../PriorDebtTable/PriorDebtTable';
import PrintPageButton from 'components/Elements/PrintPageButton/PrintPageButton';
import { useRef } from 'react';

type BalanceDueProps = ComponentProps & {
  fields: {
    BalanceTitleText: Field<string>;
    PrintLabel: Field<string>;
    BalanceDueText1: Field<string>;
    BalanceDueText2: Field<string>;
    AccountNumberLabel: Field<string>;
    FullNameLabel: Field<string>;
    ServiceAddressLabel: Field<string>;
    FinalBillDateLabel: Field<string>;
    AmountLabel: Field<string>;
    PriorBalanceLabel: Field<string>;
    TotalAmountPaidLabel: Field<string>;
    PriorBalanceDueText: Field<string>;
    PriorBalanceInfoText: Field<string>;
    TotalAmountDueLabel: Field<string>;
    CompanyNameLabel: Field<string>;
  };
  isPaymentFailed: boolean;
  paymentFailureMessage: string;
  setIsPaymentFailed: React.Dispatch<React.SetStateAction<boolean>>;
  setPaymentFailureMessage: React.Dispatch<React.SetStateAction<string>>;
};

const BalanceDue = (props: BalanceDueProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  let enrollment = undefined;
  if (!isPageEditing) enrollment = useAppSelector((state) => state.enrollment);
  const priorDebt = enrollment?.priorDebtInfo?.PriorDebt;
  const partialSuccessInfo = enrollment?.PriorDebtSuccessPaymentInfo;

  let partialSuccessInfoKeys: string[] = [];
  if (partialSuccessInfo !== undefined) {
    partialSuccessInfoKeys = Object.keys(partialSuccessInfo);
  }

  const isPartialSuccess =
    partialSuccessInfoKeys.length !== 0 && partialSuccessInfoKeys.length !== priorDebt?.length;
  let totalPriorBalance = 0;

  priorDebt?.forEach((val) => {
    if (!partialSuccessInfoKeys.includes(val.ServiceContractNumber)) {
      totalPriorBalance += val.TotalDebt;
    }
  });

  let paidPriorBalance = 0;
  partialSuccessInfoKeys?.forEach((infoKey) => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    paidPriorBalance += partialSuccessInfo[infoKey].priorDebtDetail.TotalDebt;
  });

  const priorDebtTableHeader = [
    props.fields.CompanyNameLabel.value,
    props.fields.AccountNumberLabel.value,
    props.fields.FullNameLabel.value,
    props.fields.ServiceAddressLabel.value,
    props.fields.FinalBillDateLabel.value,
    props.fields.AmountLabel.value,
  ];

  const priorDebtTableKeys = [
    'CompanyName',
    'ContractAccountNumber',
    'CustomerName',
    'Address',
    'FinalBillDate',
    'TotalDebt',
  ];

  const currencyFormat = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  });

  //if required camel case can use this function
  // function camelCase(str: string) {
  //   if (str === null || str === '') return '';
  //   else str = str.toString();

  //   return str.replace(/\w\S*/g, function (txt) {
  //     return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
  //   });
  // }

  const partialsuccessdata = partialSuccessInfoKeys.map((val) => ({
    ContractAccountNumber: partialSuccessInfo[val].priorDebtDetail.ContractAccountNumber,
    CustomerName: partialSuccessInfo[val].priorDebtDetail.CustomerName,
    Address: partialSuccessInfo[val].priorDebtDetail.Address,
    FinalBillDate: dayjs(partialSuccessInfo[val].priorDebtDetail.FinalBillDate)
      .format('MM/DD/YYYY')
      .toString(),
    TotalDebt: currencyFormat.format(partialSuccessInfo[val].priorDebtDetail.TotalDebt),
  }));

  const depositdata: Record<string, string | number>[] = [];
  priorDebt?.forEach((val) => {
    if (!partialSuccessInfo[val.ServiceContractNumber]) {
      depositdata.push({
        CompanyName: val.BrandName,
        ContractAccountNumber: val.ContractAccountNumber,
        CustomerName: val.CustomerName,
        Address: val.Address,
        FinalBillDate: dayjs(val.FinalBillDate).format('MM/DD/YYYY').toString(),
        TotalDebt: currencyFormat.format(val.TotalDebt),
      });
    }
  });

  const fulldepositdata: Record<string, string | number>[] = [];
  priorDebt?.forEach((val) => {
    if (!partialSuccessInfoKeys.includes(val.ServiceContractNumber)) {
      fulldepositdata.push({
        CompanyName: val.BrandName,
        ContractAccountNumber: val.ContractAccountNumber,
        CustomerName: val.CustomerName,
        Address: val.Address,
        FinalBillDate: dayjs(val.FinalBillDate).format('MM/DD/YYYY').toString(),
        TotalDebt: currencyFormat.format(val.TotalDebt),
      });
    }
  });

  console.log(
    'fulldepositdata',
    fulldepositdata,
    depositdata,
    priorDebtTableHeader,
    priorDebtTableKeys,
    partialsuccessdata
  );
  const printRef = useRef<HTMLDivElement>(null);
  return (
    <div
      className="prior-debt-balance-due w-full max-w-[830px] px-[15px] flex flex-col gap-5 print:w-11/12 print:m-5 wide:w-full ipad:w-full wide:px-[20px]"
      ref={printRef}
    >
      <Text
        tag="p"
        className=" text-error"
        field={{ value: props.isPaymentFailed ? props.paymentFailureMessage : '' }}
      />
      <div className="w-full md:w-[800px] md:px-0 flex flex-col gap-5 wide:w-full ipad:w-full pt-8">
        <div className="flex flex-row">
          <Text
            tag="p"
            className="font-primaryBlack   text-textPrimary text-plus1 md:flex-grow md:text-plus3"
            field={props.fields.BalanceTitleText}
          />
          <PrintPageButton label={props.fields.PrintLabel.value} printRef={printRef} />
        </div>
        <Text
          tag="p"
          className="text-minus1 pt-5 sm:pt-0"
          field={isPartialSuccess ? props.fields.BalanceDueText2 : props.fields.BalanceDueText1}
        />
        {isPartialSuccess ? (
          <PriorDebtTable
            header={priorDebtTableHeader}
            keys={priorDebtTableKeys}
            data={partialsuccessdata}
          ></PriorDebtTable>
        ) : (
          <PriorDebtTable
            header={priorDebtTableHeader}
            keys={priorDebtTableKeys}
            data={depositdata}
          ></PriorDebtTable>
        )}
        <RichText
          tag="p"
          className="font-primaryRegular text-minus1 text-textQuattuordenary md:text-lg pt-5 sm:pt-0"
          field={{
            value: isPartialSuccess
              ? `${props.fields.TotalAmountPaidLabel.value.replace(
                  '{paidPriorBalance}',
                  `${paidPriorBalance.toFixed(2)}`
                )}`
              : `${props.fields.PriorBalanceLabel.value.replace(
                  '{totalBalance}',
                  `${totalPriorBalance.toFixed(2)}`
                )}`,
          }}
        />
      </div>
      {isPartialSuccess && (
        <div className="justify-start flex flex-col sm:gap-9 w-full px-5 sm:w-[800px] sm:px-0 wide:w-full ipad:w-full wide:px-[20px] ipad:px-[40px]">
          <div className="flex flex-col gap-3 sm:gap-6 w-[335px] sm:w-[800px] pt-3">
            <div className="flex flex-row">
              <Text
                tag="h2"
                className="font-primaryBlack  text-txublue text-textPrimary text-plus1 md:flex-grow md:text-plus3"
                field={props.fields.PriorBalanceDueText}
              />
            </div>
            <Text
              tag="p"
              className="text-minus1 pt-5 sm:pt-0"
              field={props.fields.PriorBalanceInfoText}
            />
            <PriorDebtTable
              header={priorDebtTableHeader}
              keys={priorDebtTableKeys}
              data={fulldepositdata}
            ></PriorDebtTable>
            <Text
              tag="p"
              className="font-primaryRegular  text-minus1  text-textQuattuordenary md:text-lg pt-5 sm:pt-0"
              field={{
                value: `${props.fields.TotalAmountDueLabel.value}: $${totalPriorBalance.toFixed(
                  2
                )}`,
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export { BalanceDue };
const Component = withDatasourceCheck()<BalanceDueProps>(BalanceDue);
export default aiLogger(Component, Component.name);
