import {
  Text,
  Field,
  withD<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Field,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import Button from 'components/Elements/Button/Button';
import dayjs from 'dayjs';
import { ComponentProps } from 'lib/component-props';
import { useRouter } from 'next/router';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { useAppSelector } from 'src/stores/store';
import PriorDebtTable from '../PriorDebtTable/PriorDebtTable';
import { useDisclosure } from '@mantine/hooks';

type PriorDebtPaymentConfirmationProps = ComponentProps & {
  fields: {
    ConfirmationPageTitle: Field<string>;
    ConfirmationPageDescription: Field<string>;
    AccountNumberLabel: Field<string>;
    FullNameLabel: Field<string>;
    ServiceAddressLabel: Field<string>;
    FinalBillDateLabel: Field<string>;
    AmountLabel: Field<string>;
    PrintPageLabel: Field<string>;
    TotalAmountPaidLabel: Field<string>;
    ContinueButtonLabel: Field<string>;
    PlaceOrderPageLink: LinkField;
    DepositPaymentPageLink: LinkField;
    CompanyNameLabel: Field<string>;
  };
};

const PriorDebtPaymentConfirmation = (props: PriorDebtPaymentConfirmationProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  let depositAmount = 0;
  let partialSuccessInfo = undefined;
  if (!isPageEditing) {
    depositAmount = useAppSelector((state) => state?.enrollment?.depositAmount);
    partialSuccessInfo = useAppSelector((state) => state?.enrollment?.PriorDebtSuccessPaymentInfo);
  }
  let priorDebtPaid = 0;
  let partialSuccessInfoKeys: string[] = [];
  if (partialSuccessInfo !== undefined) {
    partialSuccessInfoKeys = Object.keys(partialSuccessInfo);
  }
  const [submitDisabled, { open: disableSubmit }] = useDisclosure(false);
  const router = useRouter();

  const priorDebtTableHeader = [
    props.fields.CompanyNameLabel.value,
    props.fields.AccountNumberLabel.value,
    props.fields.FullNameLabel.value,
    props.fields.ServiceAddressLabel.value,
    props.fields.FinalBillDateLabel.value,
    props.fields.AmountLabel.value,
  ];

  const priorDebtTableKeys = [
    'CompanyName',
    'ContractAccountNumber',
    'CustomerName',
    'Address',
    'FinalBillDate',
    'TotalDebt',
  ];

  const currencyFormat = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  });

  partialSuccessInfoKeys.forEach((successInfoKey) => {
    priorDebtPaid += partialSuccessInfo[successInfoKey].priorDebtDetail.TotalDebt;
  });

  const partialsuccessdata = partialSuccessInfoKeys.map((val) => ({
    CompanyName: partialSuccessInfo[val].priorDebtDetail.BrandName,
    ContractAccountNumber: partialSuccessInfo[val].priorDebtDetail.ContractAccountNumber,
    CustomerName: partialSuccessInfo[val].priorDebtDetail.CustomerName,
    Address: partialSuccessInfo[val].priorDebtDetail.Address,
    FinalBillDate: dayjs(partialSuccessInfo[val].priorDebtDetail.FinalBillDate)
      .format('MM/DD/YYYY')
      .toString(),
    TotalDebt: currencyFormat.format(partialSuccessInfo[val].priorDebtDetail.TotalDebt),
  }));

  function redirect() {
    disableSubmit();
    if (depositAmount === 0) {
      router.push({
        pathname: props.fields.PlaceOrderPageLink.value.href,
        query: { ...router.query },
      });
    } else {
      router.push({
        pathname: props.fields.DepositPaymentPageLink.value.href,
        query: {
          ...router.query,
        },
      });
    }
  }

  return (
    <div className="prior-debt-payment-confirmation justify-start flex flex-col gap-6 sm:gap-9 w-full px-5 sm:w-[800px] sm:px-0 wide:w-full ipad:w-full">
      <div className="w-full px-5 md:w-[800px] md:px-0 flex flex-col gap-5 wide:w-full ipad:w-full pt-8">
        <div className="flex flex-row">
          <Text
            tag="h2"
            className="font-primaryBlack text-textPrimary  text-plus1 sm:w-[120px] md:flex-grow md:text-plus3 w-3/6"
            field={props.fields.ConfirmationPageTitle}
          />
          <Text
            tag="a"
            className="font-primaryBlack text-textSecondary hover:text-hoverPrimary text-[20px] flex-grow md:flex-grow-0 text-right leading-[20px] cursor-pointer w-fit"
            field={props.fields.PrintPageLabel}
          />
        </div>
        <Text tag="p" field={props.fields.ConfirmationPageDescription} />
        <PriorDebtTable
          header={priorDebtTableHeader}
          keys={priorDebtTableKeys}
          data={partialsuccessdata}
        ></PriorDebtTable>
        <Text
          tag="p"
          field={{
            value: props.fields.TotalAmountPaidLabel.value.replace(
              '{{amount}}',
              priorDebtPaid.toFixed(2)
            ),
          }}
        />
      </div>
      <div className="w-full sm:w-[800px] flex justify-center sm:justify-start mt-8 wide:w-full ipad:w-full">
        <Button
          className="w-full h-[56px] md:w-[303px]"
          type="button"
          onClick={redirect}
          disabled={submitDisabled}
        >
          {props.fields.ContinueButtonLabel.value}
        </Button>
      </div>
    </div>
  );
};

export { PriorDebtPaymentConfirmation };
const Component = withDatasourceCheck()<PriorDebtPaymentConfirmationProps>(
  PriorDebtPaymentConfirmation
);
export default aiLogger(Component, Component.name);
