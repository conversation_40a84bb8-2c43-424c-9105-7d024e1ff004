import { useAppSelector } from 'src/stores/store';
import InfoText from 'components/common/InfoText/InfoText';
import {
  Field,
  Text,
  withDatasourceCheck,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import { cn } from 'src/utils/cn';
import { ComponentProps } from 'lib/component-props';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';

type PrintSelectedPlanCardProps = ComponentProps & {
  fields: {
    PlanSummary?: Field<string>;
    WhatsNextLabel?: Field<string>;
  };
};

const PrintSelectedPlanCard = (props: PrintSelectedPlanCardProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  let selectedPlan = undefined;
  if (!isPageEditing) {
    selectedPlan = useAppSelector((state) => state.plans?.selectedPlan);
  }

  return (
    <>
      <div
        className={cn('w-full  flex-col hidden bg-white h-fit p-5 md:p-8 md:flex-row gap-[10px]')}
      >
        <div className="flex flex-col gap-2 md:w-1/2">
          <Text
            tag="p"
            className="font-primaryBlack text-textQuattuordenary text-minus1 md:text-base"
            field={{ value: 'Selected Plan' }}
          />
          <InfoText label={'Plan Name'} value={selectedPlan?.planName} />
          <InfoText label={'Rate'} value={`${(selectedPlan?.rate * 100).toFixed(1)}\u00A2`} />
          <InfoText
            label={'Term'}
            value={`${selectedPlan?.term ? `${selectedPlan?.term} Months` : 'Month to Month'}`}
          />
        </div>
      </div>
      <div className={cn('hidden')}>
        <Text
          tag="p"
          className="font-primaryBold text-[32px] text-plus2 sm:text-plus3 text-textQuattuordenary leading-[38px] mt-[40px]"
          field={props?.fields?.WhatsNextLabel}
        />
        <Text
          tag="p"
          className="font-primaryRegular text-textQuattuordenary text-minus1 text-[18px] leading-[28px] mt-[14px]"
          field={props?.fields?.PlanSummary}
        />
      </div>
    </>
  );
};
export { PrintSelectedPlanCard };
const Component = withDatasourceCheck()<PrintSelectedPlanCardProps>(PrintSelectedPlanCard);
export default aiLogger(Component, Component.name);
