import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Link,
  Field,
  withDatasourceCheck,
  LinkField,
  ImageField,
  Image,
  useComponentProps,
  GetServerSideComponentProps,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import { ComponentProps } from 'lib/component-props';
import { useRouter } from 'next/router';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { removeURLParams } from 'src/utils/util';
import { faChevronsRight, faChevronUp, faChevronDown } from '@fortawesome/pro-regular-svg-icons';
import {
  faTableList,
  faCircleDollarToSlot,
  faReceipt,
  faPlug,
  faEnvelope,
  faSun,
  faGear,
  faMobileScreen,
  faGlobe,
  faRightFromBracket,
  faChartSimple,
} from '@fortawesome/pro-solid-svg-icons';
import { useAppDispatch, useAppSelector } from 'src/stores/store';
import { getSwapOrRenewalStatus } from 'src/utils/getSwap';
import { GetUserProfileResponse } from 'src/services/AuthenticationAPI/types';
import { setUserFirstName, setUserLastName } from 'src/stores/authUserSlice';
import AuthenticationAPI from 'src/services/AuthenticationAPI';
import { AxiosError } from 'axios';
import { logErrorToAppInsights } from 'lib/app-insights-log-error';
import { useEffect, useState } from 'react';
import { useLoader } from 'src/hooks/modalhooks';
import PageBuilder from 'src/components/PageBuilder/PageBuilder';
import { getCookie } from 'cookies-next';
type MenuListProps = ComponentProps & {
  fields: {
    Menus: MenuList[];
  };
};

interface MenuList {
  displayName: Field<string>;
  fields: {
    Icon: Field<string>;
    NavText: Field<string>;
    NavLink: LinkField;
    IsHidden: Field<boolean>;
    CssClass: Field<string>;
    HighlightURLs: Field<string>;
    Submenu: MenuList[];
    HideForBusinessUser: Field<boolean>;
    IconImage?: ImageField;
  };
  id: Field<string>;
  name: Field<string>;
  url: Field<string>;
}
let dispatch: ReturnType<typeof useAppDispatch>;
function MenuListComponent(props: { menu: MenuList; key: number }) {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  if (isPageEditing) return <PageBuilder componentName="Fields of MenuListComponent" />;
  let swapOrRenewal = undefined;
  let isBusinessUser = undefined;

  if (!isPageEditing) {
    swapOrRenewal = useAppSelector((state) => state.authuser?.renewal);
    isBusinessUser = useAppSelector((state) => state.authuser?.isBusinessUser);
    dispatch = useAppDispatch();
  }
  const [isToggled, setIsToggled] = useState(false);
  let isSubToggled = false;
  const router = useRouter();
  const currentPath = removeURLParams(router.asPath);
  const { openModal } = useLoader();
  const impersonatedCookieValue = getCookie('isImpersonatedUser');
  const impersonatedUser = impersonatedCookieValue === 'true';

  const Submenus = props.menu.fields.Submenu.map((submenu, submenuindex) => {
    let isSubNavHighlight = false;
    const highlightURLs: string[] = submenu.fields.HighlightURLs.value
      .split(',')
      .map((val) => val.trim());
    highlightURLs.forEach((val) => {
      if (val && currentPath.startsWith(val)) {
        isSubNavHighlight = true;
        isSubToggled = true;
      }
      if (currentPath === submenu.fields.NavLink.value.href) {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        isSubNavHighlight = true;
        isSubToggled = true;
      }
    });

    const submenuresponse = getSwapOrRenewalStatus(
      swapOrRenewal?.swapOrRenewalStatus,
      swapOrRenewal?.contractAccount,
      swapOrRenewal?.esiid,
      swapOrRenewal?.promo,
      submenu,
      impersonatedUser
    );

    if (submenuresponse) submenu.fields.NavLink.value.href = submenuresponse.navLink;

    if (props.menu.fields.HideForBusinessUser?.value === true && isBusinessUser) return null;
    else
      return (
        <li
          key={submenuindex}
          className={`${submenuresponse.allowDisplay ? 'block' : 'hidden'} ${
            submenu.fields.CssClass.value
          }`}
        >
          <Link
            field={submenu.fields.NavLink}
            className={`${
              isSubNavHighlight
                ? 'font-primaryBold text-textQuattuordenary'
                : 'text-textQuattuordenary font-primaryRegular'
            } hover:text-textSecondary text-minus1 ml-[38px]`}
            onClick={() => {
              openModal();
            }}
          >
            {submenu.fields.NavText.value}
          </Link>
        </li>
      );
  });

  const highlightURLs: string[] = props.menu.fields.HighlightURLs.value
    .split(',')
    .map((val) => val.trim());

  let isNavHighlight = false;
  highlightURLs.forEach((val) => {
    if (val && currentPath.includes(val)) {
      isNavHighlight = true;
    }
  });
  const menuresponse = getSwapOrRenewalStatus(
    swapOrRenewal?.swapOrRenewalStatus,
    swapOrRenewal?.contractAccount,
    swapOrRenewal?.esiid,
    swapOrRenewal?.promo,
    props.menu,
    impersonatedUser
  );
  if (menuresponse) props.menu.fields.NavLink.value.href = menuresponse.navLink;
  if (currentPath === props.menu.fields.NavLink.value.href && !isToggled && !isSubToggled) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    isNavHighlight = true;
  }

  useEffect(() => {
    if (!isNavHighlight && Submenus.length > 0 && !isSubToggled) {
      setIsToggled(false);
    }
    if (isSubToggled) {
      setIsToggled(true);
    }
  }, [isNavHighlight, isSubToggled]);

  const handleClick = () => {
    isNavHighlight = false;
    setIsToggled((value) => !value);
  };

  const iconMap = {
    'table-list': faTableList,
    'circle-dollar-to-slot': faCircleDollarToSlot,
    'chart-simple': faChartSimple,
    receipt: faReceipt,
    plug: faPlug,
    envelope: faEnvelope,
    sun: faSun,
    gear: faGear,
    'mobile-screen': faMobileScreen,
    globe: faGlobe,
    'right-from-bracket': faRightFromBracket,
  };

  if (props.menu.fields.HideForBusinessUser?.value === true && isBusinessUser) return null;
  else
    return (
      <li
        key={props.key}
        className={`${menuresponse.allowDisplay ? 'block' : 'hidden'} ${
          props.menu.fields.CssClass.value
        } mb-1`}
      >
        <div className="flex flex-row items-center">
          <FontAwesomeIcon
            className={`${
              isNavHighlight || isSubToggled ? 'hidden ' : 'hidden'
            } text-textPrimary text-minus2`}
            icon={faChevronsRight}
          />
          {Submenus.length == 0 && (
            <Link
              aria-current={isNavHighlight ? 'page' : undefined}
              className={`${
                isNavHighlight
                  ? 'border-solid border-cactus text-textSecondary'
                  : 'text-textQuattuordenary border-transparent'
              } leading-[24px] cursor-pointer flex items-center border-l-2 
              text-textQuattuordenary hover:text-textSecondary text-minus1 ml-0 pl-0 border-none gap-3`}
              field={{ value: props.menu.fields.NavLink.value }}
              role="menuitem"
              onClick={() => {
                openModal();
              }}
            >
              {props.menu?.fields?.NavText?.value != '' ? (
                <>
                  {props.menu?.fields?.IconImage?.value?.src ? (
                    <Image
                      className="block w-[16px] h-[16px]"
                      field={props.menu.fields.IconImage}
                    />
                  ) : props.menu?.fields?.Icon?.value ? (
                    <FontAwesomeIcon
                      className="block w-[16px]"
                      icon={
                        iconMap[props.menu.fields.Icon.value as keyof typeof iconMap] ||
                        faChevronsRight
                      }
                    />
                  ) : null}

                  <span className="my-account-showMenu relative">
                    {props.menu.fields.NavText.value}
                  </span>
                </>
              ) : null}
            </Link>
          )}
          {Submenus.length > 0 && (
            <Link
              aria-current={isNavHighlight ? 'page' : undefined}
              className={`${
                isNavHighlight || isSubToggled
                  ? 'text-textPrimary border-cactus border-none font-primaryBold'
                  : 'text-textQuattuordenary font-primaryRegular'
              } leading-[24px] cursor-pointer flex text-textQuattuordenary hover:text-textSecondary text-minus1 ml-0 pl-0 font-primaryBold border-none gap-3 items-center w-full`}
              field={{ value: props.menu.fields.NavLink.value }}
              onClick={(e) => {
                e.preventDefault();
                handleClick();
              }}
              role="menuitem"
            >
              {props.menu?.fields?.IconImage?.value?.src ? (
                <Image className="block w-[16px] h-[16px]" field={props.menu.fields.IconImage} />
              ) : (
                <FontAwesomeIcon
                  className="block w-[16px]"
                  icon={
                    iconMap[props.menu?.fields?.Icon?.value as keyof typeof iconMap] ||
                    faChevronsRight
                  }
                />
              )}

              <span className="my-account-showMenu relative">
                {props.menu.fields.NavText.value}
              </span>
            </Link>
          )}

          {Submenus.length > 0 && (
            <FontAwesomeIcon
              className="ml-auto cursor-pointer text-textSecondary"
              icon={isToggled ? faChevronUp : faChevronDown}
              onClick={() => handleClick()}
            />
          )}
        </div>
        {isToggled && (
          <div className="flex flex-row gap-3 py-3 ml-0 pl-0">
            <ul className="flex flex-col gap-3 font-primaryRegular">{Submenus}</ul>
          </div>
        )}
      </li>
    );
}

const MenuList = (props: MenuListProps): JSX.Element | null => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  if (isPageEditing) return <PageBuilder componentName="Fields of MenuList" />;
  if (!isPageEditing) {
    dispatch = useAppDispatch();
  }
  const userProfileData = useComponentProps<GetUserProfileResponse>(props.rendering?.uid);
  const custFirstName = userProfileData?.result?.firstName;
  const custLastName = userProfileData?.result?.lastName;
  if (custFirstName && custLastName) {
    if (!isPageEditing) {
      dispatch(setUserFirstName(custFirstName));
      dispatch(setUserLastName(custLastName));
    }
  }

  const menuList: MenuList[] = props.fields.Menus;

  const MenuLinks = menuList.map((menu, index) => <MenuListComponent menu={menu} key={index} />);

  return (
    <div className="">
      <div className="hidden md:block wide:hidden ipad:hidden">
        {/* menu */}
        <nav aria-label="Main navigation">
          <ul role="menu" className="grid gap-2 sm:px-5 sm:ml-2">
            {MenuLinks}
          </ul>
        </nav>
      </div>
    </div>
  );
};

export { MenuList };
const Component = withDatasourceCheck()<MenuListProps>(MenuList);
export default aiLogger(Component, Component.name);

export const getServerSideProps: GetServerSideComponentProps = async (
  _rendering,
  _layoutData,
  context
) => {
  const access_token = context.req.session.user?.access_token;
  if (access_token) {
    try {
      const userProfileReq = await AuthenticationAPI.getUserProfile(access_token as string);
      if (userProfileReq?.data != null) {
        return userProfileReq.data;
      } else {
        return null;
      }
    } catch (err: unknown) {
      const error = err as AxiosError;
      logErrorToAppInsights(error, {
        componentStack: 'MenuList - getServerSideProps',
      });
      return null;
    }
  } else {
    return null;
  }
};
