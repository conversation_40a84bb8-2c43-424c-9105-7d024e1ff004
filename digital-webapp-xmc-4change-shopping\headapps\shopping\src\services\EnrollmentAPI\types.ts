export interface ChannelVendor {
  channel?: string | null;
  vendorId?: string | null;
}

export interface ResponseErrors {
  hasErrors: boolean;
  messages: string[];
}

export type PriorDebtData = {
  BusinessPartnerNumber: string;
  ContractAccountNumber: string;
  Address: string;
  TotalDebt: number;
  CustomerName: string;
  FinalBillDate: string;
  ServiceContractNumber: string;
  BrandId: string;
  BrandName: string;
};

export interface CreateCustomerResponse extends ResponseErrors {
  result: {
    partnerNumber: string;
    fraudMatch: string;
    priorDebt: {
      ThresholdPassed: boolean;
      PriorDebt: PriorDebtData[];
    };
    hasPriorDebt: boolean;
    webErrorCode: string | null;
    priorDebtStatus: string;
  };
}

export interface CreateCustomerBody extends ChannelVendor {
  email: string;
  phoneNumber: number | undefined;
  isMobile?: boolean;
  driverLicenseNumber: string | null;
  driverLicenseState: string | null;
  socialSecurityNumber: string | null;
  city: string | null;
  firstName: string;
  lastName: string;
  middleName: string | null;
  poBox: string | null;
  postalCode: string | null;
  correspondenceLanguage: string | null;
  dateOfBirth: string | null;
  esiid: string;
  poBoxCity: string;
  poBoxState: string;
  poBoxZipCode: string;
  skiptdvalidation: boolean;
  streetName: string | null;
  streetNumber: string | null;
  unit: string | null;
  state: string | null;
  DRSActionToken: string | null;
  sessionId: string;
  CNumber?: string;
  ANumber?: string;
  MFAReferralId?: string | null;
}

export interface CheckSecurityDepositBody extends ChannelVendor {
  bpNumber: string;
  isMultiFamilyDwelling: string | null;
  priorDebtStatus?: string;
  streetName: string;
  streetNumber: string;
  city: string;
  state: string;
  postalCode: string;
  esiid?: string;
  unitNumber: string | null;
  firstName?: string;
  lastName?: string;
  socialSecurityNumber: string | null;
  mobileNumber?: number | null;
  driverLicenseNumber: string | null;
  driverLicenseState: string | null;
  dateOfBirth: string | null;
  Email: string;
}

export interface CheckSecurityDepositResponse extends ResponseErrors {
  result: {
    isDepositRequired: boolean;
    indicator: string;
    canCreateContract: boolean;
    autopayEligible: boolean;
  };
}

export interface CalculateDepositResponse extends ResponseErrors {
  result: number;
}

export interface CreateContractAccountBody extends ChannelVendor {
  bpNumber: string;
  city: string;
  streetNumber: string;
  unit?: string | null;
  postalCode: string;
  streetName: string;
  state: string;
  language?: string | null;
  country: string;
  PoBox: string | null;
  COName: string;
}

export interface CreateContractAccountResponse extends ResponseErrors {
  result: {
    contractAccount: string;
    indicator: string;
  };
}

export interface ConnectValidateBody extends ChannelVendor {
  bpNumber: string;
  contractAccountNumber: string;
  startDate: string;
  esiid: string;
  customerIntent: string;
  dwellingType?: string | null;
  productId: string;
  incentiveId?: string | null;
  promoCode: string;
  campaignId: string;
  enrollDate: string;
  WebExperienceId?: string;
  CNumber?: string;
  ANumber?: string;
  ReferralId?: string;
  autopayEligible?: boolean;
  hasNoDeposit?: boolean;
  cardDetails: CardDetails | null;
  bankDetails: BankDetails | null;
}

interface CardDetails {
  cardToken: string;
  CVV: string;
  expDate: string;
  cardHolder: string;
}
interface BankDetails {
  bankToken: string;
  profileId: string;
  routingNumber: string;
  accountHolder: string;
}

export interface ConnectValidateResponse extends ResponseErrors {
  result: {
    indicator: string;
    serviceContractNumber: string;
  };
}

export interface CreateCustomerNextBody extends ChannelVendor {
  email: string;
  phoneNumber: number | undefined;
  isMobile?: boolean;
  driverLicenseNumber: string;
  driverLicenseState: string | null;
  socialSecurityNumber: string;
  city: string;
  firstName: string;
  lastName: string;
  middleName: string | null;
  poBox: string | null;
  postalCode: string;
  correspondanceLanguage: string | null;
  dateOfBirth: string | null;
  esiid: string;
  isMultiFamilyDwelling: string | null;
  streetName: string;
  streetNumber: string;
  state: string;
  unitNumber: string;
  startDate: string;
  customerIntent: string;
  dwellingType: string | null;
  productId: string;
  incentiveId: string | null;
  promoCode: string;
  campaignId: string;
  billingStreetNumber: string;
  billingStreetAddress: string;
  billingAptOrUnit: string;
  billingCity: string;
  billingState: string;
  billingZipCode: string;
  poBoxCity: string;
  poBoxState: string;
  poBoxZipCode: string;
  isSameAddress: boolean;
  skiptdvalidation: boolean;
  enrollDate: string;
  billingOption: string;
  WebExperienceId?: string;
  DRSActionToken: string;
  sessionId: string;
  CNumber?: string;
  ANumber?: string;
  MFAReferralId?: string | null;
}

export interface CreateCustomerNextResponse {
  createCustomerResponse: CreateCustomerResponse;
  checkSecurityDepositResponse: CheckSecurityDepositResponse;
  calculateDepositResponse: CalculateDepositResponse;
  createContractAccountResponse: CreateContractAccountResponse;
  connectValidateResponse: ConnectValidateResponse;
}

export interface ConnectBody extends ChannelVendor {
  bpNumber: string;
  contractAccountNumber: string;
  startDate: string;
  esiid: string;
  customerIntent?: string | null;
  dwellingType?: string | null;
  productId: string;
  incentiveId?: string | null;
  promoCode: string;
  campaignId: string;
  enrollDate: string;
  WebExperienceId?: string;
  CNumber?: string;
  ANumber?: string;
  ReferralId: string;
  autopayEligible?: boolean;
  hasNoDeposit?: boolean;
  cardDetails: CardDetails | null;
  bankDetails: BankDetails | null;
}

export interface ConnectResponse extends ResponseErrors {
  result: {
    indicator: string;
    serviceContractNumber: string;
  };
}

export interface CreateOnlineAccountBody extends ChannelVendor {
  Username: string;
  Email: string;
  SecurityQuestion: string;
  SecurityAnswer: string;
  CustomerClassification: string;
  EnrollmentChannel: string;
  FirstName: string;
  LastName: string;
  LanguagePreference: string;
  OrganizationBPNumber: string;
  Password: string;
  PaperlessBilling: boolean;
  SpecialOffers: boolean;
  PersonBPNumber: number;
  SendEmailInvitation: boolean;
  isBusinessUser: boolean;
  AccountNumber: number;
  Portal: string;
  isEnrollment: boolean;
  BrandId?: string;
}

export interface AutoPayBody extends ChannelVendor {
  ContractAccountNumber: string;
  HoldersName: string;
  AccountId: string;
  DisplayAccountNumber: string;
  ProfileId?: string;
  PartnerNumber: string;
  routingNumber: string;
  Expiration?: string;
  CVV: string;
  mode: string;
}

export interface AutoPayResponse extends ChannelVendor {
  result: {
    indicator: string;
    message: string | null;
  };
  hasErrors: boolean;
  messages: string[];
}

export interface CreateOnlineAccountResponse extends ResponseErrors {
  result: boolean;
  hasErrors: boolean;
  messages: string[];
}

export interface PaySecurityDepositBody {
  expiration: string;
  amount: string;
  cvv: string;
  cardType: string;
  payChannel: string;
  payAgent: string;
  holderName: string;
  nickname: string;
  accountId: string;
  serviceContractNumber: string;
  profileId: string;
  paymentType: string;
  language: string;
  contractAccountNumber: string;
  paymentDate: string;
  bpNumber: string;
  billingPostalCode: string;
  tenderType: string;
  store: boolean;
  autoPayEligible: boolean;
}

export interface PaymentDepositResponse extends ResponseErrors {
  result: {
    autopayFailure: boolean;
    indicator: string;
    confirmation: string;
    message: string | null;
  };
}

export interface PriorDebtPaymentBody {
  PaymentDate: string;
  expiration: string;
  cardType: string;
  HolderName: string;
  contractAccountNumber: string;
  nickname: string;
  accountId: string;
  profileId: string;
  paymentType: number;
  payChannel: string;
  payAgent: string;
  tenderType: string;
  PaySource: string;
  bpNumber: string;
  PriorDebtCollection: PriorDebtData[];
  BillingPostalCode: string;
  Cvv: string;
}

export interface PriorDebt {
  BusinessPartnerNumber: string;
  ContractAccountNumber: string;
  Address: string;
  TotalDebt: number;
  CustomerName: string;
  FinalBillDate: string;
  ServiceContractNumber: string;
}

export interface PaymentResultCollection {
  paymentResult: {
    indicator: string;
    confirmation: string;
    message: string;
  };
  priorDebtDetail: PriorDebt;
}

export interface PriorDebtCardPaymentResponse extends ResponseErrors {
  result: {
    paymentResultCollection: PaymentResultCollection[];
  };
}

export interface ScheduleRemainingDepositBody {
  ContractAccountNumber: string;
  Expiration: string;
  CVV: string;
  HoldersName: string;
  AccountId: string;
  DisplayAccountNumber: string;
  ProfileId: string;
  PartnerNumber: string;
  PaymentDate: string;
  Amount: number;
}
export interface ScheduleRemainingDepositResponse extends ResponseErrors {
  result: {
    indicator: string;
    confirmation: string;
    status: number;
    profileId: string;
  };
}

export interface eLeaseEmailConfirmationBody {
  Data: {
    FirstName: string;
    LastName: string;
    RecipientEmail: string;
    EmailAddress: string;
    ServiceAddress: string;
    PlanName: string;
    StartDate: string;
    ContractAccount: string;
    BP: string;
  };
}

export interface eLeaseEmailConfirmationResponse extends ResponseErrors {
  result?: {
    indicator: string;
    eventInstanceId: string;
    message: string[];
  };
  errors?: unknown;
  type?: string;
  title?: string;
  status?: string;
  traceId?: string;
}

export interface GetFraudResponse extends ResponseErrors {
  result: boolean;
  hasErrors: boolean;
  messages: string[];
}

export interface FraudServiceBody {
  firstName: string;
  lastName: string;
  intent: string;
  esiid: string;
  email: string;
  phoneNumber: number;
  clientReferenceId: string;
  messageTime: string;
  middleName: string;
  addressType: string;
  poBoxNumber: string;
  street: string;
  street2: string;
  postTown: string;
  postal: string;
  stateProvinceCode: string;
  DOB: string | null;
  SSN: string;
  driverLicense: string;
  DRSActionToken: string;
  sessionId: string;
  promoCode: string;
}

export interface FraudServiceResponse {
  oowSendOTPResponse: oowSendOTPResponse;
  fraudMatch: string;
  indicator: string;
}

interface oowSendOTPResponse {
  responseIndicator: string;
  isGoodToProceedEnrollment: boolean;
  sendOTPResults: oowSendOtpResultDetails;
  initialKba: kbaOOW;
}

interface oowSendOtpResultDetails {
  clientReferenceId: string;
  score: string;
  sessionId: string;
  oneTimePwd: string;
  decision: string;
}

interface kbaOOW {
  numberOfQuestions: number;
  questionSet: Array<questionSet>;
}

interface questionSet {
  questionType: number;
  questionText: string;
  questionSelect: questionSelect;
}

interface questionSelect {
  questionChoice: Array<string>;
}
interface CommPrefDetailItem {
  CorrChannel: string;
  Value: string;
  CorrType: string;
  FormName: string;
  ContractAccount: string;
  AttrName1: string;
  AttrValue1: string;
  EsiId: string;
}
export interface CommPrefRequest {
  Partner: string;
  GlobalOptIn: boolean;
  GlobalOptOut: boolean;
  CAOptOut: boolean;
  CAOptIn: boolean;
  Details: Array<CommPrefDetailItem>;
}

export interface CommPrefBody {
  Partner: string;
  Email: string;
  isPaperlessBilling: boolean;
}
export interface CommPrefResponse extends ResponseErrors {
  result: {
    indicator: string;
    emailGlobalOpt: boolean;
    mailGlobalOpt: boolean;
    smsGlobalOpt: boolean;
  };
}
