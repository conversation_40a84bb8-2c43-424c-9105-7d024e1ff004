import { MenuList } from 'components/common/LeftMenu/MenuList/MenuList';
import { useAppSelector } from 'src/stores/store';

export interface SwapOrRenewalResult {
  allowDisplay: boolean;
  navLink: string;
}
export function getSwapOrRenewalStatus(
  status: string,
  ca: string,
  esiid: string,
  promo: string,
  menu: MenuList,
  impersonatedUser: boolean
): SwapOrRenewalResult {
  const Authuser = useAppSelector((state) => state?.authuser);

  const termMonthCount = parseInt(Authuser?.termMonthCount);
  const termUnit = Authuser?.termUnit;
  const transferEligibility = Authuser?.transferEligibility;

  const response: SwapOrRenewalResult = {
    allowDisplay: true,
    navLink: menu.fields.NavLink.value.href as string,
  };

  if (menu.fields.CssClass.value.includes('transfer-eligible') && !transferEligibility)
    response.allowDisplay = false;

  if (status === undefined || status === '') {
    // console.log('swap',submenu.fields.NavText.value,submenu.fields.CssClass.value);
    if (menu.fields.CssClass.value.includes('renewal-eligible')) response.allowDisplay = false;
    if (menu.fields.CssClass.value.includes('comm-preff-hide') && !impersonatedUser)
      response.allowDisplay = false;
  }

  if (menu.fields.CssClass.value.includes('change-eligible')) {
    if (termUnit === 'Monthly' && termMonthCount === 0) {
      response.allowDisplay = true;
    } else {
      response.allowDisplay = false;
    }
  } else {
    //console.log('renew',submenu.fields.NavText.value,submenu.fields.CssClass.value);
    if (menu.fields.CssClass.value.includes('change-eligible')) response.allowDisplay = false;

    if (response.allowDisplay) {
      const url = menu.fields.NavLink.value.href;

      if (
        url != undefined &&
        !url?.includes('cint') &&
        menu.fields.CssClass.value.includes('renewal-eligible')
      ) {
        const renew_url = new URL(url);
        renew_url.searchParams.append('cint', '3');
        renew_url.searchParams.append('contractaccount', ca);
        renew_url.searchParams.append('esiid', esiid);
        promo && renew_url.searchParams.append('prom', promo);
        menu.fields.NavLink.value.href = renew_url.href;
        response.navLink = renew_url.href;
      } else {
        response.navLink = url as string;
      }
    }
  }
  return response;
}
