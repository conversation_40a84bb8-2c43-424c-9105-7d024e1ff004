import {
  Text,
  RichText,
  withData<PERSON>ur<PERSON><PERSON><PERSON><PERSON>,
  Field,
  useSitecoreContext,
  LinkField,
} from '@sitecore-jss/sitecore-jss-nextjs';
import { ComponentProps } from 'lib/component-props';
import { useRouter } from 'next/router';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { useAppSelector } from 'src/stores/store';
import { QueryParamsMapType } from 'src/utils/query-params-mapping';
import Button from 'components/Elements/Button/Button';
import PageBuilder from 'src/components/PageBuilder/PageBuilder';

type SwtichHoldProps = ComponentProps & {
  fields: {
    data: {
      item: {
        Title: Field<string>;
        Description: Field<string>;
        _tmp_rv_description: Field<string>;
        _tmp_vst_description: Field<string>;
        GotoSummaryText: Field<string>;
        GotoSummaryLink: { jsonValue: LinkField };
        LeaveEnrollmentLink: LinkField;
      };
    };
  };
};

const SwtichHold = (props: SwtichHoldProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;

  // if (isPageEditing) return <PageBuilder componentName="SwtichHold Fields" />;
  const router = useRouter();
  const { contractaccount } = router.query as QueryParamsMapType;
  const IsAuthenticated = context?.sitecoreContext?.route?.fields?.AuthenticatedPage?.value;
  const { utm_source } = router.query as QueryParamsMapType;
  const isFuseEnabled = context?.sitecoreContext?.route?.fields?.EnableFuse?.value;
  let ca = undefined;
  if (!isPageEditing) {
    ca = useAppSelector((state) => state.enrollment?.enrollmentInfo?.contractAccountNumber);
  }
  if (IsAuthenticated) {
    ca = contractaccount;
  }
  let description = props?.fields?.data?.item?.Description.value?.replace('${ca}', ca);
  if (isFuseEnabled && utm_source === 'rv_organic') {
    description = props?.fields?.data?.item?._tmp_rv_description?.value?.replace('${ca}', ca);
  } else if (isFuseEnabled && utm_source !== 'rv_organic') {
    description = props?.fields?.data?.item?._tmp_vst_description?.value?.replace('${ca}', ca);
  }

  function exitEnrollment(): void {
    const { prom, cnumber, anumber } = router.query as QueryParamsMapType;
    const redirectUrl = '';
    const queryParams = {
      prom: prom,
      consultantid: cnumber,
      refid: anumber,
    };

    const query = Object.fromEntries(Object.entries(queryParams).filter(([_, value]) => value));

    router.push({
      pathname: redirectUrl,
      query,
    });
  }

  console.log(props.fields.data.item.Title);
  return (
    <div className="w-full sm:min-h-[800px]">
      <div className="flex gap-3 w-full items-start flex-col flex-nowrap sm:mt-[40px] max-w-[832px] px-6 sm:pl-0 ml-0 sm:ml-[350px] wide:ml-0 ipad:ml-0 ipad:pl-6 wide:pl-6 m-0 mt-6 marginzero">
        <div className="items-center sm:items-start text-left text-plus2 w-full mb-4 max-w-[650px]">
          <h1 className="text-plus1 sm:text-plus3 text-textUndenary font-primaryBold w-full max-w-[544px]">
            <Text field={props?.fields.data?.item?.Title} />
          </h1>
        </div>

        <div className="w-full max-w-[650px]">
          <p className="text-minus1 text-textUndenary font-primaryRegular leading-[28px] sm:text-base">
            <RichText
              field={{
                value: description,
              }}
              className="text-with-link"
            />

            <span
              onClick={exitEnrollment}
              className="underline text-textPrimary text-with-link font-primaryBold cursor-pointer"
            >
              {props.fields.data?.item?.LeaveEnrollmentLink?.value?.text}
            </span>
          </p>
        </div>

        <div className="mt-6 w-full">
          <Button
            className={`${IsAuthenticated ? 'visible' : 'invisible'} items-center`}
            onClick={() =>
              router.push({
                pathname: props.fields.data?.item?.GotoSummaryLink.jsonValue.value.href,
              })
            }
          >
            {props.fields.data?.item?.GotoSummaryText.value}
          </Button>
        </div>
      </div>
    </div>
  );
};

export { SwtichHold };
const Component = withDatasourceCheck()<SwtichHoldProps>(SwtichHold);
export default aiLogger(Component, Component.name);
